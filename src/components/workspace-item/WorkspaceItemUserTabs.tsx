import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader2, Play } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';
import { WorkspaceUserTabsVisibilityManager } from '@/utils/tabs';
import { WorkspaceStateSync } from '@/utils/workspaceStateSync';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

// 用户标签页状态接口 - 恢复原始完整状态
interface UserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;
  actionType: 'hide' | 'continue_hide' | 'show';
  loading: boolean;
  error?: string;
}

interface WorkspaceItemUserTabsProps {
  workspace: WorkSpace;
  isActive: boolean;
}

/**
 * 工作区用户标签页管理组件
 * 职责：处理用户标签页的显示/隐藏功能
 * 
 * 🎯 核心职责：
 * 1. 用户标签页状态管理
 * 2. 显示/隐藏切换功能
 * 3. 继续隐藏功能
 * 4. 实时状态同步
 */
export const WorkspaceItemUserTabs: React.FC<WorkspaceItemUserTabsProps> = ({
  workspace,
  isActive
}) => {
  // 用户标签页状态 - 恢复原始完整状态
  const [userTabsState, setUserTabsState] = useState<UserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    visibleUserTabs: 0,
    canContinueHiding: false,
    actionType: 'hide',
    loading: false,
  });

  // Toast 错误处理
  const { showError, showSuccess } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 加载工作区用户标签页状态
   */
  const loadUserTabsState = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的工作区用户标签页管理器
      const result = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);

      if (result.success) {
        const data = result.data!;
        setUserTabsState({
          isHidden: data.isHidden,
          hiddenTabsCount: data.hiddenTabIds.length,
          totalUserTabs: data.totalUserTabs,
          visibleUserTabs: data.visibleUserTabs,
          canContinueHiding: data.canContinueHiding,
          actionType: data.actionType,
          loading: false,
        });
      } else {
        setUserTabsState(prev => ({
          ...prev,
          loading: false,
          error: result.error?.message || '获取用户标签页状态失败',
        }));
      }
    } catch (error) {
      console.error('加载用户标签页状态失败:', error);
      setUserTabsState(prev => ({
        ...prev,
        loading: false,
        error: '加载用户标签页状态失败',
      }));
    }
  };

  /**
   * 切换用户标签页可见性（恢复原始实现）
   */
  const toggleUserTabsVisibility = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的 WorkspaceUserTabsVisibilityManager 和 UserTabsRealTimeMonitor
      const result = await WorkspaceUserTabsVisibilityManager.toggleWorkspaceUserTabsVisibility(workspace);

      if (result.success) {
        // 立即触发实时监测更新
        const { UserTabsRealTimeMonitor } = await import('@/utils/tabs');
        await UserTabsRealTimeMonitor.triggerImmediateStateCheck();

        // 强制刷新当前工作区状态
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspace.id);

        // 重新获取状态
        await loadUserTabsState();

        // 修复：移除Toast提示，静默操作
      } else {
        errorHandler.handle(result.error!, '切换用户标签页可见性失败');
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换用户标签页可见性失败:', error);
      errorHandler.handle(error, '切换用户标签页可见性失败');
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 继续隐藏新的用户标签页（恢复原始功能）
   */
  const continueHideUserTabs = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的 WorkspaceUserTabsVisibilityManager
      const result = await WorkspaceUserTabsVisibilityManager.continueHideWorkspaceUserTabs(workspace.id);

      if (result.success) {
        console.log(`✅ 继续隐藏完成，隐藏了 ${result.data!.affectedTabsCount} 个新的用户标签页`);

        // 立即触发实时监测更新
        const { UserTabsRealTimeMonitor } = await import('@/utils/tabs');
        await UserTabsRealTimeMonitor.triggerImmediateStateCheck();

        // 强制刷新当前工作区状态
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspace.id);

        // 重新获取状态
        await loadUserTabsState();

        // 修复：移除Toast提示，静默操作
      } else {
        errorHandler.handle(result.error!, '继续隐藏用户标签页失败');
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('继续隐藏用户标签页失败:', error);
      errorHandler.handle(error, '继续隐藏用户标签页失败');
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  // 重复的函数已删除，使用上面定义的continueHideUserTabs

  // 设置状态监听器
  useEffect(() => {
    const setupStateListener = async () => {
      const handleStateUpdate = (workspaceId: string, eventType: 'switch' | 'userTabsVisibility') => {
        if (workspaceId === workspace.id) {
          console.log(`📊 收到工作区 ${workspace.name} 的实时状态更新: ${eventType}`);
          
          if (eventType === 'userTabsVisibility') {
            // 重新加载用户标签页状态
            loadUserTabsState();
          }
        }
      };

      // 使用静态导入的 WorkspaceStateSync
      const removeListener = WorkspaceStateSync.addStateListener(handleStateUpdate);

      return removeListener;
    };

    setupStateListener().then(removeListener => {
      return () => {
        if (removeListener) {
          removeListener();
        }
      };
    });
  }, [workspace.id, workspace.name]);

  // 初始加载状态 - 只在工作区活跃时加载
  useEffect(() => {
    if (isActive) {
      loadUserTabsState();
    }
  }, [workspace.id, isActive]);

  // 渲染用户标签页控制按钮（修复：恢复isActive限制）
  const renderUserTabsButton = () => {
    // 修复：只在工作区活跃时显示按钮，与旧版本保持一致
    if (!isActive) {
      return null;
    }

    const { hiddenTabsCount, totalUserTabs, visibleUserTabs, canContinueHiding, actionType, loading } = userTabsState;

    // 修复：按钮总是显示，根据不同状态决定是否禁用
    // 隐藏功能：没有用户标签页时禁用
    // 显示功能：有隐藏的标签页时可以点击
    const shouldDisable = loading || (actionType === 'show' ? hiddenTabsCount === 0 : totalUserTabs === 0);

    if (loading) {
      return (
        <button
          disabled
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded hover:bg-gray-200 transition-colors"
        >
          <Loader2 className="w-3 h-3 animate-spin" />
          <span>处理中...</span>
        </button>
      );
    }

    // 修复渲染逻辑：根据状态正确禁用按钮
    return (
      <>
        {/* 用户标签页隐藏/显示按钮 - 修复：正确的禁用逻辑 */}
        <div className="relative">
          <button
            onClick={toggleUserTabsVisibility}
            disabled={shouldDisable}
            className={`p-2 rounded transition-colors duration-150 ${
              shouldDisable
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-slate-600'
            }`}
            title={
              actionType === 'show'
                ? hiddenTabsCount === 0
                  ? '没有隐藏的用户标签页'
                  : `显示 ${hiddenTabsCount} 个隐藏的用户标签页`
                : totalUserTabs === 0
                ? '没有用户标签页'
                : actionType === 'continue_hide'
                ? `隐藏所有 ${totalUserTabs} 个用户标签页`
                : `隐藏 ${visibleUserTabs} 个用户标签页`
            }
          >
            {loading ? (
              <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
            ) : actionType === 'show' ? (
              <Eye className="w-4 h-4 text-slate-400 hover:text-blue-400" />
            ) : (
              <EyeOff className="w-4 h-4 text-slate-400 hover:text-orange-400" />
            )}
            {/* 显示用户标签页计数 */}
            {!loading && totalUserTabs > 0 && (
              <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {totalUserTabs}
              </span>
            )}
          </button>
        </div>

        {/* 继续隐藏按钮 - 当工作区处于隐藏状态且有新的用户标签页时显示 */}
        {actionType === 'continue_hide' && canContinueHiding && (
          <button
            onClick={continueHideUserTabs}
            disabled={loading}
            className={`p-2 rounded transition-colors duration-150 ${
              loading
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-slate-600 bg-orange-600/20'
            }`}
            title={`继续隐藏 ${visibleUserTabs} 个新创建的用户标签页`}
          >
            {loading ? (
              <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
            ) : (
              <Play className="w-4 h-4 text-orange-400" />
            )}
          </button>
        )}
      </>
    );
  };

  return (
    <div className="flex items-center gap-2">
      {renderUserTabsButton()}
    </div>
  );
};

export default WorkspaceItemUserTabs;
