import {
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { WorkonaTabManager } from '../workonaTabManager';
import { TabClassificationUtils } from './TabClassificationUtils';
import { WindowManager } from '../windowManager';

/**
 * 工作区标签页移动管理器
 * 职责：处理工作区切换时的标签页移动逻辑
 * 
 * 🎯 核心职责：
 * 1. 标签页在窗口间的移动操作
 * 2. 工作区专属窗口的创建和管理
 * 3. 标签页移动的批量处理和优化
 * 4. 移动过程中的错误处理和回滚
 */
export class WorkspaceTabMover {
  /**
   * 移动非目标工作区的标签页到专用窗口（重构：按照旧代码逻辑重新实现）
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);

      // 获取所有工作区
      const { StorageManager } = await import('../storage');
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log('获取工作区列表失败:', workspacesResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaces = workspacesResult.data!;

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);

      // 检查每个非目标工作区，看是否有真正管理的标签页需要移动
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue; // 跳过目标工作区
        }

        // 使用Workona ID血缘关系识别工作区管理的标签页
        const relatedTabs: chrome.tabs.Tab[] = [];

        for (const tab of currentTabs) {
          if (!tab.url || !tab.id) continue;

          // 基于Workona ID血缘关系检查标签页归属
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

          if (workonaIdResult.success && workonaIdResult.data) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);

            if (metadataResult.success && metadataResult.data?.workspaceId === workspace.id) {
              relatedTabs.push(tab);
            }
          }
        }

        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个真正管理的标签页需要移动到专用窗口`);

          const tabIds = relatedTabs.map(tab => tab.id!);

          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );

          if (moveResult.success) {
            console.log(`✅ 成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`❌ 移动工作区 "${workspace.name}" 标签页失败:`, moveResult.error);
          }
        } else {
          console.log(`工作区 "${workspace.name}" 在当前窗口中没有真正管理的标签页`);
        }
      }

      return { success: true };
    } catch (error) {
      console.error('移动非目标工作区标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move non-target workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 将当前标签页移动到工作区专属窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 将当前窗口的所有标签页移动到工作区 ${workspace.name} 的专用窗口 (完整会话隔离)`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 筛选出需要移动的标签页（排除系统标签页）
      const tabsToMove: number[] = [];
      const systemTabsCount = allTabs.filter(tab =>
        tab.url && TabClassificationUtils.isSystemTab(tab.url)
      ).length;

      for (const tab of allTabs) {
        // 排除系统标签页
        if (tab.url && TabClassificationUtils.isSystemTab(tab.url)) {
          console.log(`🚫 跳过系统标签页: ${tab.url}`);
          continue;
        }

        // 包含所有非系统标签页（工作区核心标签页 + 会话临时标签页）
        if (tab.id) {
          tabsToMove.push(tab.id);
          console.log(`📦 准备移动标签页: ${tab.title} (${tab.url})`);
        }
      }

      console.log(`📊 当前窗口状态: 总标签页 ${allTabs.length} 个，系统标签页 ${systemTabsCount} 个，需移动 ${tabsToMove.length} 个`);

      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 当前窗口没有需要移动的标签页`);
        return { success: true };
      }

      // 移动标签页到专用窗口
      console.log(`🚀 准备移动 ${tabsToMove.length} 个标签页到专用窗口`);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabsToMove,
        workspace.id,
        workspace.name
      );

      if (moveResult.success) {
        console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到工作区专用窗口`);

        // 移动完成后再次检查窗口状态
        const finalTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        const finalSystemTabsCount = finalTabs.filter(tab =>
          tab.url && TabClassificationUtils.isSystemTab(tab.url)
        ).length;

        console.log('📊 移动后窗口状态:', {
          剩余标签页: finalTabs.length,
          系统标签页: finalSystemTabsCount,
          用户标签页: finalTabs.length - finalSystemTabsCount
        });

        return { success: true };
      } else {
        return { success: false, error: moveResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 将标签页从工作区窗口移动回主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 将标签页从工作区 "${workspace.name}" 窗口移动回主窗口`);

      // 获取所有窗口
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      
      // 查找工作区相关的标签页
      const tabsToMove: number[] = [];

      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }

        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;

          // 跳过系统标签页
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }

          // 检查是否属于目标工作区
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split('-')[1];
            if (tabWorkspaceId === workspace.id) {
              tabsToMove.push(tab.id);
            }
          }
        }
      }

      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动回主窗口的标签页`);
        return { success: true };
      }

      // 移动标签页到当前窗口
      await chrome.tabs.move(tabsToMove, {
        windowId: currentWindow.id!,
        index: -1
      });

      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页回主窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }





  /**
   * 保存工作区的固定状态到 Workona ID 映射中（补充缺失的方法）
   */
  static async saveWorkspacePinnedStates(workspace: WorkSpace, tabs: chrome.tabs.Tab[]): Promise<void> {
    try {
      console.log(`💾 保存工作区 "${workspace.name}" 的固定状态到 Workona ID 映射...`);

      let savedCount = 0;

      for (const tab of tabs) {
        if (tab.pinned && tab.id) {
          // 获取标签页的 Workona ID
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;

            // 获取现有的元数据
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
            const existingMetadata = metadataResult.success ? metadataResult.data?.metadata : undefined;

            // 更新映射中的固定状态
            await WorkonaTabManager.updateTabMetadata(workonaId, {
              metadata: {
                ...existingMetadata,
                source: existingMetadata?.source || 'workspace_website',
                isPinned: true,
                pinnedAt: Date.now()
              }
            });

            console.log(`💾 保存固定状态: ${workonaId} -> Chrome ID ${tab.id} (${tab.title})`);
            savedCount++;
          } else {
            console.warn(`⚠️ 无法找到标签页 ${tab.id} 的 Workona ID，跳过固定状态保存`);
          }
        }
      }

      console.log(`✅ 工作区 "${workspace.name}" 固定状态保存完成: ${savedCount} 个标签页`);
    } catch (error) {
      console.error('保存工作区固定状态失败:', error);
    }
  }

}
