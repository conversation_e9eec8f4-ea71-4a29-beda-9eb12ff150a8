import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager, WorkspaceTabContentMatcher, WorkspaceUserTabsVisibilityManager, UserTabsRealTimeMonitor } from './tabs';
import { WorkspaceSessionManager } from './workspaceSessionManager';
import { WorkonaTabManager } from './workonaTabManager';
import { ERROR_CODES } from './constants';

// 重新导出拆分后的类，保持向后兼容
export {
  TabClassificationUtils,
  WorkspaceSwitchCore,
  WorkspaceTabMover,
  WorkspaceProtectionManager,
  WorkspaceNotificationManager
} from './workspace-switching';

// 导入拆分后的类
import {
  TabClassificationUtils,
  WorkspaceSwitchCore,
  WorkspaceTabMover,
  WorkspaceProtectionManager,
  WorkspaceNotificationManager
} from './workspace-switching';

/**
 * 工作区切换管理类 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的1,704行workspaceSwitcher.ts文件已按照单一职责原则拆分为5个专门的服务类：
 * - TabClassificationUtils: 标签页分类和识别工具 (./workspace-switching/TabClassificationUtils.ts)
 * - WorkspaceSwitchCore: 工作区切换核心逻辑和状态管理 (./workspace-switching/WorkspaceSwitchCore.ts)
 * - WorkspaceTabMover: 标签页移动和窗口管理 (./workspace-switching/WorkspaceTabMover.ts)
 * - WorkspaceProtectionManager: 系统保护机制和异常处理 (./workspace-switching/WorkspaceProtectionManager.ts)
 * - WorkspaceNotificationManager: 通知管理和状态同步 (./workspace-switching/WorkspaceNotificationManager.ts)
 * 
 * 📋 向后兼容：
 * 本文件保留了原有的主要API接口，确保现有代码无需修改即可使用新的模块结构
 */
export class WorkspaceSwitcher {
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress(): boolean {
    return WorkspaceSwitchCore.isSetupInProgress();
  }

  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId(): string | null {
    return WorkspaceSwitchCore.getCurrentSetupWorkspaceId();
  }

  /**
   * 主要的工作区切换方法
   * 整合了所有拆分后的服务类功能
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 开始切换到工作区: ${workspaceId}`);

      // 🚨 第一步：立即执行系统保护机制（修复：在切换开始时就确保系统标签页存在）
      console.log(`🛡️ 执行系统保护机制...`);
      await WorkspaceProtectionManager.ensureSystemTabProtection();

      // 第二步：验证切换请求
      const validationResult = await WorkspaceSwitchCore.validateSwitchRequest(workspaceId);
      if (!validationResult.success) {
        return { success: false, error: validationResult.error };
      }
      const workspace = validationResult.data!;

      // 第三步：检查是否需要切换
      const shouldSwitch = await WorkspaceSwitchCore.shouldPerformSwitch(workspaceId);
      if (!shouldSwitch) {
        return { success: true }; // 已在目标工作区，无需切换
      }

      // 第四步：准备切换选项
      const optionsResult = await WorkspaceSwitchCore.prepareSwitchOptions(options);
      if (!optionsResult.success) {
        return { success: false, error: optionsResult.error };
      }
      const switchOptions = optionsResult.data!;

      // 第五步：获取当前工作区
      const currentWorkspaceResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 第六步：设置后台设置状态
      WorkspaceSwitchCore.setBackgroundSetupStatus(workspaceId, true);

      // 第七步：执行工作区切换保护机制
      await WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);

      // 第八步：保存当前工作区状态
      if (currentWorkspace) {
        console.log(`💾 保存当前工作区状态: ${currentWorkspace.name}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      }

      // 第九步：处理标签页移动
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        // 从一个工作区切换到另一个工作区
        await WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        // 从无工作区状态切换到工作区
        await WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }

      // 第十步：从目标工作区专用窗口移动标签页回主窗口（修复：补充缺失的关键步骤）
      console.log(`📥 从目标工作区专用窗口移动标签页回主窗口: ${workspace.name}`);
      await WorkspaceTabMover.moveTabsFromWorkspaceWindow(workspace);

      // 第十一步：处理等待归属的标签页
      await this.handlePendingTabAssignment(workspaceId);

      // 第十二步：打开工作区网站（如果需要）
      if (switchOptions.autoOpenWebsites) {
        await this.openWorkspaceWebsites(workspace);
      }

      // 第十三步：处理用户标签页可见性
      await this.handleUserTabsVisibilityState(workspace);

      // 第十四步：完成切换操作
      const finalizeResult = await WorkspaceSwitchCore.finalizeSwitchOperation(workspaceId);
      if (!finalizeResult.success) {
        return finalizeResult;
      }

      // 第十五步：发送通知
      await WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
      await WorkspaceNotificationManager.notifyWorkspaceSetupStatusChange(workspaceId, false);

      console.log(`✅ 工作区切换完成: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      // 清理后台设置状态
      WorkspaceSwitchCore.setBackgroundSetupStatus(null, false);
      
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    return WorkspaceSwitchCore.getCurrentWorkspace();
  }

  /**
   * 检测活跃工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      // 策略1：从存储中获取活跃工作区
      const currentResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      if (currentResult.success && currentResult.data) {
        return currentResult;
      }

      // 策略2：通过活跃标签页检测
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success || !activeTabResult.data) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data;

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;

      // 通过Workona ID检测
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workspaceId = workonaIdResult.data.split('-')[1];
        const workspace = workspaces.find(w => w.id === workspaceId);
        if (workspace) {
          console.log(`🎯 通过Workona ID检测到活跃工作区: ${workspace.name}`);
          return { success: true, data: workspace };
        }
      }

      // 策略3：通过内容匹配检测
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find(w => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }

      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 处理等待归属的标签页
   */
  private static async handlePendingTabAssignment(workspaceId: string): Promise<void> {
    try {
      console.log(`🔄 处理等待归属的标签页，分配到工作区: ${workspaceId}`);

      // 获取所有等待归属的标签页
      const allTabs = await chrome.tabs.query({});
      const pendingTabs = [];

      for (const tab of allTabs) {
        if (!tab.id || !tab.url) continue;

        // 跳过系统标签页
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 检查是否为等待归属的标签页
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId === 'pending-assignment') {
            pendingTabs.push(tab);
          }
        }
      }

      if (pendingTabs.length === 0) {
        console.log(`ℹ️ 没有等待归属的标签页`);
        return;
      }

      // 重新分配等待归属的标签页
      for (const tab of pendingTabs) {
        if (!tab.id) continue; // 跳过没有ID的标签页

        const oldWorkonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (oldWorkonaIdResult.success && oldWorkonaIdResult.data) {
          // 删除旧的映射
          await WorkonaTabManager.removeTabMapping(oldWorkonaIdResult.data);

          // 创建新的映射
          const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          await WorkonaTabManager.createTabIdMapping(
            newWorkonaId,
            tab.id,
            workspaceId,
            undefined,
            {
              isWorkspaceCore: false,
              source: 'user_opened'
            }
          );

          console.log(`✅ 重新分配标签页: ${tab.title} -> ${workspaceId}`);
        }
      }

      console.log(`✅ 完成 ${pendingTabs.length} 个等待归属标签页的重新分配`);
    } catch (error) {
      console.error('处理等待归属标签页失败:', error);
    }
  }

  /**
   * 打开工作区网站
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<void> {
    try {
      console.log(`🌐 打开工作区网站: ${workspace.name}`);

      for (const website of workspace.websites) {
        // 检查网站是否已经打开
        const existingTabs = await chrome.tabs.query({ url: website.url + '*' });
        if (existingTabs.length > 0) {
          console.log(`ℹ️ 网站已打开，跳过: ${website.title}`);
          continue;
        }

        // 创建新标签页
        const createResult = await TabManager.createTab(website.url, false, false);
        if (createResult.success) {
          console.log(`✅ 打开网站: ${website.title}`);
        } else {
          console.error(`❌ 打开网站失败: ${website.title}`, createResult.error);
        }
      }
    } catch (error) {
      console.error('打开工作区网站失败:', error);
    }
  }

  /**
   * 处理用户标签页可见性状态
   */
  private static async handleUserTabsVisibilityState(workspace: WorkSpace): Promise<void> {
    try {
      console.log(`👁️ 处理工作区用户标签页可见性: ${workspace.name}`);

      // 获取用户标签页状态
      const stateResult = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);
      if (stateResult.success && stateResult.data) {
        const state = stateResult.data;
        console.log(`📊 用户标签页状态: 总计 ${state.totalUserTabs}, 可见 ${state.visibleUserTabs}, 隐藏 ${state.hiddenTabIds.length}`);

        // 触发实时监控更新
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspace.id);
      }
    } catch (error) {
      console.error('处理用户标签页可见性状态失败:', error);
    }
  }

  /**
   * 向后兼容的方法 - 委托给新的服务类
   */

  // 系统保护相关
  static async ensureSystemTabProtection(): Promise<void> {
    return WorkspaceProtectionManager.ensureSystemTabProtection();
  }

  static async ensureWorkspaceSwitchProtection(workspaceId: string): Promise<void> {
    return WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);
  }

  // 标签页移动相关
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    return WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId);
  }

  static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    return WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(workspace);
  }

  // 通知相关
  static async notifyWorkspaceSwitchComplete(workspaceId: string): Promise<void> {
    return WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
  }
}
