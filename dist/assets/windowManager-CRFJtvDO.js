import { E as ERROR_CODES, S as StorageManager, b as WorkonaTabManager } from './dataMigration-B7rEP7ww.js';

class WindowManager {
  static globalWorkspaceWindowId = null;
  // 全局专用窗口ID
  static GLOBAL_WORKSPACE_WINDOW_KEY = "global_workspace_window";
  static isCreatingWindow = false;
  // 防止并发创建窗口
  /**
   * 从存储中恢复全局窗口ID
   */
  static async loadGlobalWindowId() {
    try {
      const result = await chrome.storage.local.get([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      if (result[this.GLOBAL_WORKSPACE_WINDOW_KEY]) {
        this.globalWorkspaceWindowId = result[this.GLOBAL_WORKSPACE_WINDOW_KEY];
      }
    } catch (error) {
      console.warn("加载全局窗口ID失败:", error);
    }
  }
  /**
   * 保存全局窗口ID到存储
   */
  static async saveGlobalWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [this.GLOBAL_WORKSPACE_WINDOW_KEY]: windowId
      });
      this.globalWorkspaceWindowId = windowId;
    } catch (error) {
      console.warn("保存全局窗口ID失败:", error);
    }
  }
  /**
   * 清理全局窗口ID
   */
  static async clearGlobalWindowId() {
    try {
      await chrome.storage.local.remove([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      this.globalWorkspaceWindowId = null;
    } catch (error) {
      console.warn("清理全局窗口ID失败:", error);
    }
  }
  /**
   * 获取或创建全局专用窗口（单例模式）
   */
  static async getOrCreateGlobalWorkspaceWindow() {
    try {
      console.log("🪟 获取或创建全局工作区专用窗口");
      if (this.isCreatingWindow) {
        console.log("⏳ 正在创建窗口中，等待完成...");
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        return await this.getOrCreateGlobalWorkspaceWindow();
      }
      if (!this.globalWorkspaceWindowId) {
        await this.loadGlobalWindowId();
      }
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId);
          if (window) {
            console.log(`✅ 全局工作区专用窗口已存在: ${this.globalWorkspaceWindowId}`);
            return {
              success: true,
              data: {
                id: this.globalWorkspaceWindowId,
                workspaceId: "global",
                workspaceName: "全局工作区专用窗口",
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== "minimized"
              }
            };
          }
        } catch {
          console.log("🗑️ 全局专用窗口已不存在，需要重新创建");
          await this.clearGlobalWindowId();
        }
      }
      this.isCreatingWindow = true;
      try {
        console.log("🔨 创建新的全局工作区专用窗口");
        const window = await chrome.windows.create({
          type: "normal",
          state: "normal",
          focused: false,
          // 不获取焦点
          width: 1200,
          height: 800,
          left: 100,
          top: 100,
          url: chrome.runtime.getURL("workspace-placeholder.html") + "?workspaceId=global&workspaceName=" + encodeURIComponent("全局工作区专用窗口")
        });
        if (!window.id) {
          throw new Error("Failed to create global workspace window");
        }
        await this.saveGlobalWindowId(window.id);
        try {
          await chrome.windows.update(window.id, { state: "minimized" });
        } catch (error) {
          console.warn("最小化窗口失败，但窗口创建成功:", error);
        }
      } finally {
        this.isCreatingWindow = false;
      }
      console.log(`✅ 成功创建全局工作区专用窗口 -> 窗口ID ${this.globalWorkspaceWindowId}`);
      return {
        success: true,
        data: {
          id: this.globalWorkspaceWindowId,
          workspaceId: "global",
          workspaceName: "全局工作区专用窗口",
          tabCount: 1,
          // 新创建的窗口包含一个占位符标签页
          isVisible: false
          // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建全局工作区专用窗口失败`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to create global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 为工作区创建专用窗口（保持向后兼容）
   * 现在所有工作区都使用同一个全局专用窗口
   */
  static async createWorkspaceWindow(_workspaceId, _workspaceName) {
    return await this.getOrCreateGlobalWorkspaceWindow();
  }
  /**
   * 获取工作区的专用窗口ID（现在所有工作区共享同一个窗口）
   */
  static getWorkspaceWindowId(_workspaceId) {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 获取窗口对应的工作区ID（现在返回全局标识）
   */
  static getWindowWorkspaceId(windowId) {
    return windowId === this.globalWorkspaceWindowId ? "global" : void 0;
  }
  /**
   * 获取全局专用窗口ID
   */
  static getGlobalWorkspaceWindowId() {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 将标签页移动到全局专用窗口
   */
  static async moveTabsToWorkspaceWindow(tabIds, workspaceId, workspaceName) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`🔄 开始移动 ${tabIds.length} 个标签页到全局专用窗口（来自工作区: ${workspaceName}）`, {
        tabIds,
        workspaceId,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      const windowResult = await this.getOrCreateGlobalWorkspaceWindow();
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }
      const windowId = windowResult.data.id;
      console.log(`🔍 开始验证 ${tabIds.length} 个标签页的有效性...`);
      const validTabIds = [];
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          validTabIds.push(tabId);
          console.log(`📋 验证标签页 ${tabId}: "${tab.title}" (${tab.url}) - 有效`);
        } catch (error) {
          console.warn(`⚠️ 标签页 ${tabId} 不存在或无法访问，跳过移动:`, error);
        }
      }
      if (validTabIds.length === 0) {
        console.log(`⚠️ 没有有效的标签页需要移动`);
        return { success: true };
      }
      try {
        if (validTabIds.length === 0) {
          console.log(`⚠️ 验证后发现没有有效的标签页需要移动`);
          return { success: true };
        }
        const safeTabIds = validTabIds.filter((id) => id && typeof id === "number" && id > 0);
        if (safeTabIds.length === 0) {
          console.log(`⚠️ 过滤后没有安全的标签页ID可以移动`);
          return { success: true };
        }
        if (safeTabIds.length !== validTabIds.length) {
          console.warn(`⚠️ 发现 ${validTabIds.length - safeTabIds.length} 个无效的标签页ID，已过滤`);
        }
        await chrome.tabs.move(safeTabIds, {
          windowId,
          index: -1
          // 移动到窗口末尾
        });
        console.log(`✅ 成功移动 ${safeTabIds.length} 个标签页到专用窗口 ${windowId}`);
      } catch (moveError) {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveError);
        throw moveError;
      }
      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将特定工作区的标签页从全局专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspaceId, targetWindowId) {
    try {
      console.log(`从全局专用窗口移动工作区 ${workspaceId} 的标签页到主窗口`);
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在`);
        return { success: true, data: [] };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        console.log(`获取工作区 ${workspaceId} 信息失败:`, workspaceResult.error);
        return { success: true, data: [] };
      }
      const workspace = workspaceResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const tabs = await chrome.tabs.query({ windowId });
      const workspaceTabs = [];
      console.log(`🔍 检查专用窗口中的 ${tabs.length} 个标签页，识别属于工作区 "${workspace.name}" 的标签页...`);
      for (const tab of tabs) {
        if (tab.url?.includes("workspace-placeholder.html")) {
          console.log(`🚫 跳过占位符页面: ${tab.url}`);
          continue;
        }
        if (!tab.id) {
          console.log(`⚠️ 标签页无ID，跳过: ${tab.url}`);
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workonaId = workonaIdResult.data;
          const workspaceIdFromMapping = workonaId.split("-")[1];
          if (workspaceIdFromMapping === workspaceId) {
            workspaceTabs.push(tab);
            console.log(`✅ 找到工作区标签页: ${tab.title} (${tab.url}) - Workona ID: ${workonaId}`);
          } else {
            console.log(`❌ 标签页属于其他工作区: ${tab.title} - 工作区ID: ${workspaceIdFromMapping}`);
          }
        } else {
          const isWorkspaceTab = workspaceUrls.some((url) => tab.url?.startsWith(url));
          if (isWorkspaceTab) {
            workspaceTabs.push(tab);
            console.log(`✅ 通过URL匹配找到工作区标签页: ${tab.title} (${tab.url})`);
          } else {
            console.log(`❌ 非工作区标签页: ${tab.title} (${tab.url})`);
          }
        }
      }
      console.log(`📊 在全局专用窗口中找到工作区 "${workspace.name}" 的 ${workspaceTabs.length} 个标签页`);
      if (workspaceTabs.length === 0) {
        console.log(`全局专用窗口中没有工作区 "${workspace.name}" 的标签页需要移动`);
        return { success: true, data: [] };
      }
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id;
      }
      console.log(`从全局专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);
      const tabIds = workspaceTabs.map((tab) => tab.id);
      console.log(`🚀 移动 ${tabIds.length} 个标签页到主窗口 ${targetWindow}`);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabIds.length} 个标签页到主窗口`);
      const tabInfos = workspaceTabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow,
        index: tab.index
      }));
      console.log(`✅ 成功移动 ${workspaceTabs.length} 个标签页到主窗口（包括会话临时标签页）`);
      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从全局专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeWorkspaceWindow(_workspaceId) {
    return await this.closeGlobalWorkspaceWindow();
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeGlobalWorkspaceWindow() {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在，无需关闭`);
        return { success: true };
      }
      console.log(`关闭全局专用窗口: ${windowId}`);
      await this.moveTabsFromWorkspaceWindow("global");
      await chrome.windows.remove(windowId);
      this.globalWorkspaceWindowId = null;
      console.log(`成功关闭全局专用窗口: ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`关闭全局专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to close global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区专用窗口信息（现在只有一个全局窗口）
   */
  static async getAllWorkspaceWindows() {
    try {
      const windowInfos = [];
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId, { populate: true });
          windowInfos.push({
            id: this.globalWorkspaceWindowId,
            workspaceId: "global",
            workspaceName: "全局工作区专用窗口",
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== "minimized"
          });
        } catch {
          this.globalWorkspaceWindowId = null;
        }
      }
      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to get workspace windows",
          details: error
        }
      };
    }
  }
  /**
   * 更新窗口标题（现在更新全局窗口标题）
   */
  static async updateWindowTitle(_workspaceId, _workspaceName, tabCount) {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        return { success: true };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(
        (tab) => tab.url?.includes("workspace-placeholder.html")
      );
      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=global&workspaceName=${encodeURIComponent("全局工作区专用窗口")}&tabCount=${tabCount}`;
        await chrome.tabs.update(placeholderTab.id, { url: newUrl });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to update window title",
          details: error
        }
      };
    }
  }
}

export { WindowManager };
