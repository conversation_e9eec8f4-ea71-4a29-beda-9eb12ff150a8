import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { TabClassificationUtils } from './TabClassificationUtils';

/**
 * 工作区保护机制管理器
 * 职责：确保工作区切换过程中的系统稳定性和数据安全
 * 
 * 🎯 核心职责：
 * 1. 系统标签页保护（防止意外关闭重要系统页面）
 * 2. 工作区切换保护（防止数据丢失和状态混乱）
 * 3. 标签页状态验证和恢复
 * 4. 异常情况的检测和处理
 */
export class WorkspaceProtectionManager {
  /**
   * 确保系统标签页保护
   * 防止重要的系统标签页被意外关闭或移动
   */
  static async ensureSystemTabProtection(): Promise<void> {
    try {
      console.log('🛡️ 执行系统标签页保护检查');

      // 获取当前窗口的所有标签页
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      
      // 检查是否有重要的系统标签页
      const systemTabs = currentTabs.filter(tab => 
        tab.url && TabClassificationUtils.isSystemTab(tab.url)
      );

      if (systemTabs.length > 0) {
        console.log(`🛡️ 发现 ${systemTabs.length} 个系统标签页，确保其保护状态`);
        
        // 确保系统标签页不会被意外操作
        for (const tab of systemTabs) {
          if (tab.id) {
            // 这里可以添加具体的保护逻辑
            // 例如：标记为不可关闭、不可移动等
            console.log(`🛡️ 保护系统标签页: ${tab.url}`);
          }
        }
      }

      // 检查是否至少有一个可用的标签页
      const nonSystemTabs = currentTabs.filter(tab => 
        tab.url && !TabClassificationUtils.isSystemTab(tab.url)
      );

      if (nonSystemTabs.length === 0) {
        console.warn('⚠️ 当前窗口没有非系统标签页，可能需要创建占位符标签页');
        
        // 创建一个新标签页，防止窗口关闭（修复：使用chrome://newtab/而不是about:blank）
        await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: false
        });
        
        console.log('✅ 已创建占位符标签页');
      }

    } catch (error) {
      console.error('系统标签页保护失败:', error);
    }
  }

  /**
   * 确保工作区切换保护
   * 防止切换过程中的数据丢失和状态混乱
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId: string): Promise<void> {
    try {
      console.log(`🛡️ 执行工作区切换保护检查 (目标: ${targetWorkspaceId})`);

      // 检查当前窗口状态
      const currentWindow = await chrome.windows.getCurrent();
      if (!currentWindow.id) {
        throw new Error('无法获取当前窗口ID');
      }

      // 检查当前窗口的标签页数量
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🛡️ 当前窗口有 ${currentTabs.length} 个标签页`);

      // 确保至少有一个标签页存在
      if (currentTabs.length === 0) {
        console.warn('⚠️ 当前窗口没有标签页，创建占位符标签页');
        await chrome.tabs.create({
          url: 'about:blank',
          windowId: currentWindow.id,
          active: true
        });
      }

      // 检查是否有活跃标签页
      const activeTabs = currentTabs.filter(tab => tab.active);
      if (activeTabs.length === 0 && currentTabs.length > 0) {
        console.warn('⚠️ 没有活跃标签页，激活第一个标签页');
        const firstTab = currentTabs[0];
        if (firstTab.id) {
          await chrome.tabs.update(firstTab.id, { active: true });
        }
      }

      console.log('✅ 工作区切换保护检查完成');
    } catch (error) {
      console.error('工作区切换保护失败:', error);
    }
  }

  /**
   * 验证标签页状态完整性
   */
  static async validateTabStateIntegrity(): Promise<OperationResult<void>> {
    try {
      console.log('🔍 验证标签页状态完整性');

      // 获取所有窗口和标签页
      const allWindows = await chrome.windows.getAll({ populate: true });
      let totalTabs = 0;
      let systemTabs = 0;
      let userTabs = 0;

      for (const window of allWindows) {
        if (!window.tabs) continue;

        for (const tab of window.tabs) {
          if (!tab.url) continue;
          
          totalTabs++;
          
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            systemTabs++;
          } else {
            userTabs++;
          }
        }
      }

      console.log(`📊 标签页统计: 总计 ${totalTabs}, 系统 ${systemTabs}, 用户 ${userTabs}`);

      // 检查是否有异常情况
      if (totalTabs === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No tabs found in any window',
          },
        };
      }

      if (allWindows.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: 'No windows found',
          },
        };
      }

      console.log('✅ 标签页状态完整性验证通过');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to validate tab state integrity',
          details: error,
        },
      };
    }
  }

  /**
   * 检测并处理孤立标签页
   * 查找没有正确Workona ID映射的标签页
   */
  static async detectAndHandleOrphanedTabs(): Promise<OperationResult<number>> {
    try {
      console.log('🔍 检测孤立标签页');

      const allWindows = await chrome.windows.getAll({ populate: true });
      const orphanedTabs: chrome.tabs.Tab[] = [];

      for (const window of allWindows) {
        if (!window.tabs) continue;

        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;

          // 跳过系统标签页
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }

          // 检查是否有有效的Workona ID映射
          // 这里可以添加具体的检测逻辑
          // 目前简化处理
        }
      }

      console.log(`📊 发现 ${orphanedTabs.length} 个孤立标签页`);

      // 这里可以添加孤立标签页的处理逻辑
      // 例如：重新分配到默认工作区、提示用户处理等

      return { success: true, data: orphanedTabs.length };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect orphaned tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 紧急恢复机制
   * 在检测到严重问题时执行的恢复操作
   */
  static async emergencyRecovery(): Promise<OperationResult<void>> {
    try {
      console.log('🚨 执行紧急恢复机制');

      // 检查是否有可用的窗口
      const allWindows = await chrome.windows.getAll();
      if (allWindows.length === 0) {
        // 创建新窗口
        await chrome.windows.create({
          url: 'about:blank',
          focused: true,
          type: 'normal'
        });
        console.log('🚨 已创建紧急恢复窗口');
      }

      // 确保至少有一个标签页
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      
      if (currentTabs.length === 0) {
        await chrome.tabs.create({
          url: 'about:blank',
          windowId: currentWindow.id,
          active: true
        });
        console.log('🚨 已创建紧急恢复标签页');
      }

      console.log('✅ 紧急恢复完成');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYSTEM_ERROR,
          message: 'Emergency recovery failed',
          details: error,
        },
      };
    }
  }
}
