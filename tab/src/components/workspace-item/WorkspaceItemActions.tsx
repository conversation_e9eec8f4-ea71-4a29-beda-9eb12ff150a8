import React, { useState } from 'react';
import { MoreVertical, Edit, Trash2, Link, CheckSquare } from 'lucide-react';
import { WorkSpace, Website } from '@/types/workspace';
import DropdownMenu from '../DropdownMenu';
import EditWorkspaceModal from '../EditWorkspaceModal';
import AddWebsiteModal from '../AddWebsiteModal';
import EditWebsiteModal from '../EditWebsiteModal';
import ConfirmDialog from '../ConfirmDialog';

interface WorkspaceItemActionsProps {
  workspace: WorkSpace;
  isActive: boolean;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onEnterBatchMode?: () => void;
}

/**
 * 工作区操作组件
 * 职责：处理工作区的各种用户操作
 * 
 * 🎯 核心职责：
 * 1. 工作区编辑和删除
 * 2. 网站添加和编辑
 * 3. 模态框状态管理
 * 4. 操作菜单管理
 */
export const WorkspaceItemActions: React.FC<WorkspaceItemActionsProps> = ({
  workspace,
  isActive: _isActive,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab: _onAddCurrentTab,
  onAddWebsiteUrl,
  onUpdateWebsite,
  onEnterBatchMode
}) => {
  // 模态框状态
  const [showDropdown, setShowDropdown] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditWebsiteModal, setShowEditWebsiteModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);

  /**
   * 处理菜单点击
   */
  const handleMenuClick = (action: string) => {
    setShowDropdown(false);

    switch (action) {
      case 'edit':
        setShowEditModal(true);
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
      case 'add-current':
        _onAddCurrentTab();
        break;
      case 'add-url':
        setShowAddModal(true);
        break;
      case 'batch-operations':
        if (onEnterBatchMode) {
          onEnterBatchMode();
        }
        break;
      default:
        console.log('未知操作:', action);
    }
  };

  /**
   * 处理工作区编辑
   */
  const handleEditWorkspace = (updates: { name?: string; icon?: string; color?: string }) => {
    onUpdateWorkspace(updates);
    setShowEditModal(false);
  };

  /**
   * 处理删除确认
   */
  const handleDeleteConfirm = () => {
    onDeleteWorkspace();
    setShowDeleteConfirm(false);
  };

  /**
   * 处理添加网站
   */
  const handleAddWebsite = (url: string) => {
    onAddWebsiteUrl(url);
    setShowAddModal(false);
  };



  /**
   * 处理保存网站编辑
   */
  const handleSaveWebsiteEdit = (updates: { url?: string; title?: string; isPinned?: boolean }) => {
    if (editingWebsite) {
      onUpdateWebsite(editingWebsite.id, updates);
      setEditingWebsite(null);
      setShowEditWebsiteModal(false);
    }
  };

  /**
   * 处理取消网站编辑
   */
  const handleCancelWebsiteEdit = () => {
    setEditingWebsite(null);
    setShowEditWebsiteModal(false);
  };

  // 构建菜单项 - 恢复原版本逻辑
  const menuItems = [
    {
      id: 'add-url',
      label: '添加网站URL',
      icon: Link,
    },
    ...(workspace.websites.length > 0 ? [{
      id: 'batch-operations',
      label: '批量操作',
      icon: CheckSquare,
    }] : []),
    {
      id: 'edit',
      label: '编辑工作区',
      icon: Edit,
    },
    {
      id: 'delete',
      label: '删除工作区',
      icon: Trash2,
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  return (
    <>
      {/* 更多操作菜单 - 恢复原版本样式 */}
      <div className="relative">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowDropdown(!showDropdown);
          }}
          className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
        >
          <MoreVertical className="w-4 h-4 text-slate-400" />
        </button>

        {/* 下拉菜单 */}
        {showDropdown && (
          <DropdownMenu
            items={menuItems}
            onItemClick={handleMenuClick}
            onClose={() => setShowDropdown(false)}
          />
        )}
      </div>

      {/* 编辑工作区模态框 */}
      {showEditModal && (
        <EditWorkspaceModal
          workspace={workspace}
          onSave={handleEditWorkspace}
          onClose={() => setShowEditModal(false)}
        />
      )}

      {/* 添加网站模态框 */}
      {showAddModal && (
        <AddWebsiteModal
          onAdd={handleAddWebsite}
          onClose={() => setShowAddModal(false)}
        />
      )}

      {/* 编辑网站模态框 */}
      {showEditWebsiteModal && editingWebsite && (
        <EditWebsiteModal
          website={editingWebsite}
          onSave={handleSaveWebsiteEdit}
          onClose={handleCancelWebsiteEdit}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <ConfirmDialog
          title="删除工作区"
          message={`确定要删除工作区"${workspace.name}"吗？此操作无法撤销。`}
          confirmText="删除"
          cancelText="取消"
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteConfirm(false)}

        />
      )}
    </>
  );
};

// 导出网站编辑相关的钩子
export const useWebsiteActions = (
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void,
  onRemoveWebsite: (websiteId: string) => void
) => {
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [showEditWebsiteModal, setShowEditWebsiteModal] = useState(false);

  const handleEditWebsite = (website: Website) => {
    setEditingWebsite(website);
    setShowEditWebsiteModal(true);
  };

  const handleSaveWebsiteEdit = (updates: { url?: string; title?: string; isPinned?: boolean }) => {
    if (editingWebsite) {
      onUpdateWebsite(editingWebsite.id, updates);
      setEditingWebsite(null);
      setShowEditWebsiteModal(false);
    }
  };

  const handleCancelWebsiteEdit = () => {
    setEditingWebsite(null);
    setShowEditWebsiteModal(false);
  };

  const handleRemoveWebsite = (websiteId: string) => {
    onRemoveWebsite(websiteId);
  };

  return {
    editingWebsite,
    showEditWebsiteModal,
    handleEditWebsite,
    handleSaveWebsiteEdit,
    handleCancelWebsiteEdit,
    handleRemoveWebsite,
  };
};

export default WorkspaceItemActions;
