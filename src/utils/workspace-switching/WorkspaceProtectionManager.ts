import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { TabClassificationUtils } from './TabClassificationUtils';

/**
 * 工作区保护机制管理器
 * 按照旧代码逻辑重新实现，确保工作区切换过程中的系统稳定性
 */
export class WorkspaceProtectionManager {
  /**
   * 系统标签页保护机制：确保窗口始终保留至少一个系统标签页
   * 在工作区切换前检查并创建必要的系统标签页，防止窗口意外关闭
   */
  static async ensureSystemTabProtection(): Promise<void> {
    try {
      console.log('🛡️ [系统保护] 开始检查系统标签页保护机制...');

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      console.log(`🔍 [系统保护] 当前窗口共有 ${allTabs.length} 个标签页`);

      // 使用TabClassificationUtils检查系统标签页
      const systemTabs = allTabs.filter(tab =>
        tab.url && TabClassificationUtils.isSystemTab(tab.url)
      );

      console.log(`📊 [系统保护] 发现 ${systemTabs.length} 个系统标签页`);

      if (systemTabs.length > 0) {
        console.log('✅ [系统保护] 窗口已有系统标签页，无需创建保护标签页');
        systemTabs.forEach(tab => {
          console.log(`   - ${tab.title} (${tab.url})`);
        });
        return;
      }

      // 如果没有系统标签页，创建一个保护标签页
      console.log('🆕 [系统保护] 窗口缺少系统标签页，创建保护标签页...');

      const protectionTab = await chrome.tabs.create({
        windowId: currentWindow.id,
        url: 'chrome://newtab/',
        active: false, // 不获得焦点
        index: 0 // 放在第一个位置
      });

      if (protectionTab.id) {
        console.log(`✅ [系统保护] 成功创建保护标签页: ${protectionTab.id} (chrome://newtab/)`);
      } else {
        console.warn('⚠️ [系统保护] 保护标签页创建成功但未获得ID');
      }
    } catch (error) {
      console.error('❌ [系统保护] 系统标签页保护机制执行失败:', error);

      // 错误处理：尝试创建紧急保护标签页
      try {
        console.log('🆘 [系统保护] 尝试创建紧急保护标签页...');
        const emergencyTab = await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: false
        });

        if (emergencyTab.id) {
          console.log(`✅ [系统保护] 紧急保护标签页创建成功: ${emergencyTab.id}`);
        }
      } catch (emergencyError) {
        console.error('❌ [系统保护] 紧急保护标签页创建失败:', emergencyError);
      }
    }
  }

  /**
   * 增强工作区切换保护：确保切换后窗口不会为空
   * 按照旧代码逻辑重新实现，防止重复创建新标签页
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId: string): Promise<void> {
    try {
      console.log(`🛡️ [切换保护] 检查工作区切换保护机制: ${targetWorkspaceId}`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      let allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      console.log(`🔍 [切换保护] 当前窗口共有 ${allTabs.length} 个标签页`);

      // 🚨 关键修复：首先检查当前窗口是否存在新标签页
      const hasNewTab = allTabs.some(tab =>
        tab.url === 'chrome://newtab/' ||
        tab.url === 'chrome://new-tab-page/' ||
        tab.pendingUrl === 'chrome://newtab/'
      );

      console.log(`🔍 [切换保护] 当前窗口是否有新标签页: ${hasNewTab}`);

      // 如果没有新标签页，立即创建一个作为窗口保护
      if (!hasNewTab) {
        console.log(`🆘 [切换保护] 缺少新标签页，立即创建窗口保护标签页...`);

        const protectionTab = await chrome.tabs.create({
          windowId: currentWindow.id,
          url: 'chrome://newtab/',
          active: false,
          index: 0
        });

        if (protectionTab.id) {
          console.log(`✅ [切换保护] 成功创建窗口保护标签页: ${protectionTab.id}`);

          // 🚨 关键修复：等待新标签页完全创建完毕
          console.log(`⏳ [切换保护] 等待新标签页完全加载...`);

          let retryCount = 0;
          const maxRetries = 10; // 最多重试10次
          const retryDelay = 100; // 每次重试间隔100ms

          while (retryCount < maxRetries) {
            try {
              // 检查标签页是否真正存在且可访问
              const verifyTab = await chrome.tabs.get(protectionTab.id);
              if (verifyTab && (verifyTab.status === 'complete' || verifyTab.url === 'chrome://newtab/')) {
                console.log(`✅ [切换保护] 新标签页已完全创建并加载完毕: ${verifyTab.status}`);
                break;
              }

              // 如果还在加载中，等待一段时间后重试
              console.log(`⏳ [切换保护] 新标签页仍在加载中，等待重试... (${retryCount + 1}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              retryCount++;

            } catch (error) {
              console.warn(`⚠️ [切换保护] 检查新标签页状态失败，重试... (${retryCount + 1}/${maxRetries})`, error);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              retryCount++;
            }
          }

          if (retryCount >= maxRetries) {
            console.warn(`⚠️ [切换保护] 新标签页创建验证超时，但继续执行切换流程`);
          }

          // 🚨 关键修复：更新标签页列表，包含新创建的保护标签页
          allTabs.unshift(protectionTab);
        } else {
          console.error(`❌ [切换保护] 创建窗口保护标签页失败`);
          throw new Error('无法创建窗口保护标签页');
        }
      } else {
        console.log(`✅ [切换保护] 当前窗口已有新标签页，窗口安全`);
      }

      console.log('✅ 工作区切换保护检查完成');
    } catch (error) {
      console.error('工作区切换保护失败:', error);
    }
  }
}
