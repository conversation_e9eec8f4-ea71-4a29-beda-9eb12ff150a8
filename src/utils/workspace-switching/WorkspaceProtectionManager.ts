import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { TabClassificationUtils } from './TabClassificationUtils';

/**
 * 工作区保护机制管理器
 * 职责：确保工作区切换过程中的系统稳定性和数据安全
 * 
 * 🎯 核心职责：
 * 1. 系统标签页保护（防止意外关闭重要系统页面）
 * 2. 工作区切换保护（防止数据丢失和状态混乱）
 * 3. 标签页状态验证和恢复
 * 4. 异常情况的检测和处理
 */
export class WorkspaceProtectionManager {
  /**
   * 确保系统标签页保护
   * 防止重要的系统标签页被意外关闭或移动
   */
  static async ensureSystemTabProtection(): Promise<void> {
    try {
      console.log('🛡️ 执行系统标签页保护检查');

      // 获取当前窗口的所有标签页
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      
      // 检查是否有重要的系统标签页
      const systemTabs = currentTabs.filter(tab => 
        tab.url && TabClassificationUtils.isSystemTab(tab.url)
      );

      if (systemTabs.length > 0) {
        console.log(`🛡️ 发现 ${systemTabs.length} 个系统标签页，确保其保护状态`);
        
        // 确保系统标签页不会被意外操作
        for (const tab of systemTabs) {
          if (tab.id) {
            // 这里可以添加具体的保护逻辑
            // 例如：标记为不可关闭、不可移动等
            console.log(`🛡️ 保护系统标签页: ${tab.url}`);
          }
        }
      }

      // 检查是否至少有一个可用的标签页
      const nonSystemTabs = currentTabs.filter(tab => 
        tab.url && !TabClassificationUtils.isSystemTab(tab.url)
      );

      if (nonSystemTabs.length === 0) {
        console.warn('⚠️ 当前窗口没有非系统标签页，可能需要创建占位符标签页');
        
        // 创建一个新标签页，防止窗口关闭（修复：使用chrome://newtab/而不是about:blank）
        await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: false
        });
        
        console.log('✅ 已创建占位符标签页');
      }

    } catch (error) {
      console.error('系统标签页保护失败:', error);
    }
  }

  /**
   * 确保工作区切换保护
   * 防止切换过程中的数据丢失和状态混乱
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId: string): Promise<void> {
    try {
      console.log(`🛡️ 执行工作区切换保护检查 (目标: ${targetWorkspaceId})`);

      // 检查当前窗口状态
      const currentWindow = await chrome.windows.getCurrent();
      if (!currentWindow.id) {
        throw new Error('无法获取当前窗口ID');
      }

      // 获取当前窗口的所有标签页
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🔍 [切换保护] 当前窗口共有 ${allTabs.length} 个标签页`);

      // 🚨 关键修复：首先检查当前窗口是否存在新标签页
      const hasNewTab = allTabs.some(tab =>
        tab.url === 'chrome://newtab/' ||
        tab.url === 'chrome://new-tab-page/' ||
        tab.pendingUrl === 'chrome://newtab/'
      );

      console.log(`🔍 [切换保护] 当前窗口是否有新标签页: ${hasNewTab}`);

      // 如果没有新标签页，立即创建一个作为窗口保护
      if (!hasNewTab) {
        console.log(`🆘 [切换保护] 缺少新标签页，立即创建窗口保护标签页...`);

        const protectionTab = await chrome.tabs.create({
          windowId: currentWindow.id,
          url: 'chrome://newtab/',
          active: false,
          index: 0
        });

        if (protectionTab.id) {
          console.log(`✅ [切换保护] 成功创建窗口保护标签页: ${protectionTab.id}`);

          // 🚨 关键修复：等待新标签页完全创建完毕
          console.log(`⏳ [切换保护] 等待新标签页完全加载...`);

          let retryCount = 0;
          const maxRetries = 10; // 最多重试10次
          const retryDelay = 100; // 每次重试间隔100ms

          while (retryCount < maxRetries) {
            try {
              // 检查标签页是否真正存在且可访问
              const verifyTab = await chrome.tabs.get(protectionTab.id);
              if (verifyTab && (verifyTab.status === 'complete' || verifyTab.url === 'chrome://newtab/')) {
                console.log(`✅ [切换保护] 新标签页已完全创建并加载完毕: ${verifyTab.status}`);
                break;
              }

              // 如果还在加载中，等待一段时间后重试
              console.log(`⏳ [切换保护] 新标签页仍在加载中，等待重试... (${retryCount + 1}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              retryCount++;

            } catch (error) {
              console.warn(`⚠️ [切换保护] 检查新标签页状态失败，重试... (${retryCount + 1}/${maxRetries})`, error);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              retryCount++;
            }
          }

          if (retryCount >= maxRetries) {
            console.warn(`⚠️ [切换保护] 新标签页创建验证超时，但继续执行切换流程`);
          }
        } else {
          console.error(`❌ [切换保护] 创建窗口保护标签页失败`);
          throw new Error('无法创建窗口保护标签页');
        }
      } else {
        console.log(`✅ [切换保护] 当前窗口已有新标签页，窗口安全`);
      }

      // 检查目标工作区是否有可恢复的标签页
      const { StorageManager } = await import('../storage');
      const targetWorkspaceResult = await StorageManager.getWorkspace(targetWorkspaceId);
      if (!targetWorkspaceResult.success || !targetWorkspaceResult.data) {
        console.warn(`⚠️ [切换保护] 无法获取目标工作区信息: ${targetWorkspaceId}`);
        return;
      }

      const targetWorkspace = targetWorkspaceResult.data;
      const hasWorkspaceWebsites = targetWorkspace.websites && targetWorkspace.websites.length > 0;

      console.log(`📊 [切换保护] 目标工作区 "${targetWorkspace.name}" 有 ${targetWorkspace.websites?.length || 0} 个网站`);

      // 检查是否有工作区专属标签页在专用窗口中
      const { WindowManager } = await import('../windowManager');
      const globalWindowId = WindowManager.getGlobalWorkspaceWindowId();
      let hasWorkspaceTabs = false;

      if (globalWindowId) {
        try {
          // 获取专用窗口中的标签页
          const windowTabs = await chrome.tabs.query({ windowId: globalWindowId });
          // 检查是否有属于目标工作区的标签页
          for (const tab of windowTabs) {
            if (tab.id) {
              const { WorkonaTabManager } = await import('../workonaTabManager');
              const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
              if (workonaIdResult.success && workonaIdResult.data) {
                const workspaceId = workonaIdResult.data.split('-')[1];
                if (workspaceId === targetWorkspaceId) {
                  hasWorkspaceTabs = true;
                  break;
                }
              }
            }
          }
          console.log(`📊 [切换保护] 目标工作区在专用窗口中有标签页: ${hasWorkspaceTabs}`);
        } catch (error) {
          console.warn(`⚠️ [切换保护] 检查专用窗口标签页失败:`, error);
        }
      } else {
        console.log(`📊 [切换保护] 没有全局专用窗口`);
      }

      // 额外保护：如果目标工作区没有网站也没有专用窗口中的标签页，且当前窗口标签页仍然很少，创建额外保护标签页
      const updatedTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      if (!hasWorkspaceWebsites && !hasWorkspaceTabs && updatedTabs.length <= 2) {
        console.log(`🆕 [切换保护] 目标工作区缺少内容且当前窗口标签页较少，创建额外保护标签页...`);

        const additionalProtectionTab = await chrome.tabs.create({
          windowId: currentWindow.id,
          url: 'chrome://newtab/',
          active: false,
          index: 1 // 放在第二个位置，避免与第一个保护标签页冲突
        });

        if (additionalProtectionTab.id) {
          console.log(`✅ [切换保护] 成功创建额外保护标签页: ${additionalProtectionTab.id}`);
        }
      } else {
        console.log(`✅ [切换保护] 目标工作区有足够内容或当前窗口安全，无需额外保护标签页`);
      }

      // 检查是否有活跃标签页
      const activeTabs = updatedTabs.filter((tab: chrome.tabs.Tab) => tab.active);
      if (activeTabs.length === 0 && updatedTabs.length > 0) {
        console.warn('⚠️ 没有活跃标签页，激活第一个标签页');
        const firstTab = updatedTabs[0];
        if (firstTab.id) {
          await chrome.tabs.update(firstTab.id, { active: true });
        }
      }

      console.log('✅ 工作区切换保护检查完成');
    } catch (error) {
      console.error('工作区切换保护失败:', error);
    }
  }

  /**
   * 验证标签页状态完整性
   */
  static async validateTabStateIntegrity(): Promise<OperationResult<void>> {
    try {
      console.log('🔍 验证标签页状态完整性');

      // 获取所有窗口和标签页
      const allWindows = await chrome.windows.getAll({ populate: true });
      let totalTabs = 0;
      let systemTabs = 0;
      let userTabs = 0;

      for (const window of allWindows) {
        if (!window.tabs) continue;

        for (const tab of window.tabs) {
          if (!tab.url) continue;
          
          totalTabs++;
          
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            systemTabs++;
          } else {
            userTabs++;
          }
        }
      }

      console.log(`📊 标签页统计: 总计 ${totalTabs}, 系统 ${systemTabs}, 用户 ${userTabs}`);

      // 检查是否有异常情况
      if (totalTabs === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No tabs found in any window',
          },
        };
      }

      if (allWindows.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: 'No windows found',
          },
        };
      }

      console.log('✅ 标签页状态完整性验证通过');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to validate tab state integrity',
          details: error,
        },
      };
    }
  }

  /**
   * 检测并处理孤立标签页
   * 查找没有正确Workona ID映射的标签页
   */
  static async detectAndHandleOrphanedTabs(): Promise<OperationResult<number>> {
    try {
      console.log('🔍 检测孤立标签页');

      const allWindows = await chrome.windows.getAll({ populate: true });
      const orphanedTabs: chrome.tabs.Tab[] = [];

      for (const window of allWindows) {
        if (!window.tabs) continue;

        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;

          // 跳过系统标签页
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }

          // 检查是否有有效的Workona ID映射
          // 这里可以添加具体的检测逻辑
          // 目前简化处理
        }
      }

      console.log(`📊 发现 ${orphanedTabs.length} 个孤立标签页`);

      // 这里可以添加孤立标签页的处理逻辑
      // 例如：重新分配到默认工作区、提示用户处理等

      return { success: true, data: orphanedTabs.length };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect orphaned tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 紧急恢复机制
   * 在检测到严重问题时执行的恢复操作
   */
  static async emergencyRecovery(): Promise<OperationResult<void>> {
    try {
      console.log('🚨 执行紧急恢复机制');

      // 检查是否有可用的窗口
      const allWindows = await chrome.windows.getAll();
      if (allWindows.length === 0) {
        // 创建新窗口
        await chrome.windows.create({
          url: 'about:blank',
          focused: true,
          type: 'normal'
        });
        console.log('🚨 已创建紧急恢复窗口');
      }

      // 确保至少有一个标签页
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      
      if (currentTabs.length === 0) {
        await chrome.tabs.create({
          url: 'about:blank',
          windowId: currentWindow.id,
          active: true
        });
        console.log('🚨 已创建紧急恢复标签页');
      }

      console.log('✅ 紧急恢复完成');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYSTEM_ERROR,
          message: 'Emergency recovery failed',
          details: error,
        },
      };
    }
  }
}
