/**
 * 标签页管理模块统一导出
 * 
 * 🎯 模块职责分离：
 * - TabManager: 基础标签页操作
 * - TabClassificationService: 标签页分类和自动归属
 * - UserTabsRealTimeMonitor: 用户标签页实时监控
 * 
 * 📋 重构说明：
 * 原来的2,305行tabs.ts文件已按照单一职责原则拆分为多个专门的服务类
 * 每个类只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// 基础标签页操作
export { TabManager } from './TabManager';

// 标签页分类服务
export { TabClassificationService } from './TabClassificationService';

// 用户标签页实时监控
export { UserTabsRealTimeMonitor } from './UserTabsRealTimeMonitor';

// 工作区用户标签页可见性管理器（重构版）
export { WorkspaceUserTabsVisibilityManager } from './WorkspaceUserTabsVisibilityManager';

// 为了保持向后兼容，重新导出一些常用的类
// 这些将在后续的重构中逐步迁移到新的模块结构

// 临时兼容性导出 - 这些类仍在原tabs.ts中，将在后续重构中拆分
export {
  WorkspaceTabContentMatcher,
  UserTabsUtils
} from '../tabs';
